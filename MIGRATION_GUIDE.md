# 从 Selenium 迁移到 Playwright 指南

本指南帮助您从 `crawler.py` (Selenium版本) 迁移到 `crawler_playwright.py` (Playwright版本)。

## 快速迁移

### 1. 安装依赖

```bash
# 安装Playwright
pip install playwright

# 安装浏览器
playwright install chromium
```

### 2. 更新导入语句

**之前 (Selenium版本):**
```python
from crawler import crawl_articles
import selenium_diver_change
```

**现在 (Playwright版本):**
```python
from crawler_playwright import crawl_articles
# 不再需要 selenium_diver_change
```

### 3. 更新函数调用

大部分参数保持不变，只需要更新浏览器参数：

**之前:**
```python
result = crawl_articles(
    input_url="https://example.com",
    base_url="https://example.com",
    browser="firefox",  # firefox, chrome, edge
    diver={"headless": True}
)
```

**现在:**
```python
result = crawl_articles(
    input_url="https://example.com", 
    base_url="https://example.com",
    browser="chromium",  # chromium, firefox, webkit
    diver={"headless": True}
)
```

## 详细对比

### 浏览器支持变化

| Selenium | Playwright | 说明 |
|----------|------------|------|
| `"firefox"` | `"firefox"` | 保持不变 |
| `"chrome"` | `"chromium"` | Chrome → Chromium |
| `"edge"` | `"chromium"` | Edge → Chromium |
| ❌ | `"webkit"` | 新增Safari支持 |

### 配置参数变化

**diver参数保持兼容:**
```python
diver = {
    "headless": True,           # 无变化
    "window_size": "1200,800",  # 无变化  
    "page_load_strategy": "normal"  # 无变化
}
```

### 新增功能

Playwright版本新增了一些功能：

```python
# 新的浏览器管理器（可选使用）
from crawler_playwright import PlaywrightManager

manager = PlaywrightManager(
    browser_type="chromium",
    headless=True
)
page = manager.start()
# 自定义操作...
manager.quit()
```

## 迁移检查清单

- [ ] 安装Playwright: `pip install playwright`
- [ ] 安装浏览器: `playwright install`
- [ ] 更新导入语句
- [ ] 更新浏览器参数 (chrome→chromium, edge→chromium)
- [ ] 测试基本功能
- [ ] 验证爬取结果
- [ ] 更新部署脚本

## 常见问题

### Q: 为什么选择Playwright？
A: Playwright相比Selenium有以下优势：
- 更快的启动和执行速度
- 更好的稳定性和可靠性
- 内置自动等待机制
- 支持更多浏览器（包括WebKit）
- 更现代化的API设计

### Q: 原有的配置文件需要修改吗？
A: 大部分配置保持不变，只需要将浏览器类型从 `chrome` 改为 `chromium`，从 `edge` 改为 `chromium`。

### Q: 性能有提升吗？
A: 是的，Playwright通常比Selenium快20-50%，特别是在启动时间和页面加载方面。

### Q: 如何处理现有的Selenium特定代码？
A: 如果您有自定义的Selenium代码，可以：
1. 使用PlaywrightManager类进行类似操作
2. 参考Playwright文档进行API转换
3. 保持两个版本并行，逐步迁移

### Q: 遇到兼容性问题怎么办？
A: 
1. 检查选择器是否正确
2. 确认目标网站是否有变化
3. 尝试不同的采集模式 (fast/safe/balance)
4. 查看错误日志进行调试

## 回滚方案

如果遇到问题需要回滚到Selenium版本：

1. 保留原有的 `crawler.py` 文件
2. 恢复原有的导入语句
3. 恢复原有的浏览器参数

## 测试验证

运行以下命令验证迁移是否成功：

```bash
# 测试Playwright版本
python crawler_playwright.py

# 运行对比测试
python test_comparison.py
```

## 获得帮助

如果在迁移过程中遇到问题：

1. 查看 `README_playwright.md` 了解详细用法
2. 运行 `test_comparison.py` 进行功能对比
3. 检查Playwright官方文档: https://playwright.dev/python/

## 最佳实践

1. **渐进式迁移**: 先在测试环境验证，再部署到生产环境
2. **保留备份**: 保留原有的Selenium版本作为备份
3. **性能监控**: 迁移后监控性能变化
4. **错误处理**: 利用Playwright更好的错误处理机制
5. **并发优化**: 考虑使用Playwright的并发特性提升效率

迁移完成后，您将享受到更快、更稳定的网页爬取体验！
