from openai import OpenAI
import selenium_diver_change
import crawler_playwright
from playwright.sync_api import sync_playwright
from playwright_work import PlaywrightUtils

# 配置DeepSeek API (替换为你的实际API密钥)
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
MODEL_NAME = "deepseek-chat"

# 初始化OpenAI客户端
client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url=DEEPSEEK_BASE_URL)

def clean_decorative_elements(html_content):
    """
    清洗HTML中的装饰元素，保留网页结构
    
    参数:
        html_content (str): 原始HTML内容
    返回:
        str: 清洗后的HTML内容
    """
    with sync_playwright() as p:
        # 使用浏览器解析HTML并清理
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        page.set_content(html_content)
        
        # 移除常见的装饰元素
        selectors_to_remove = [
            "header[role='banner']",  # 头部区域
            "footer[role='contentinfo']",  # 尾部区域
            "aside",  # 侧边栏
            "nav",  # 导航栏
            "div[id*='ad'], div[class*='ad'], div[id*='banner'], div[class*='banner'], div[id*='widget'], div[class*='widget']",  # 广告和小部件
            "script",  # 脚本
            "style"   # 样式表
        ]
        
        # 保留主要内容容器
        selectors_to_keep = [
            "main",
            "article",
            "section.content",
            "div.container",
            "div.row"
        ]
        
        # 先处理需要移除的元素
        for selector in selectors_to_remove:
            try:
                elements = page.query_selector_all(selector)
                for element in elements:
                    element.remove()
            except Exception as e:
                continue  # 忽略不存在的选择器
        
        # 确保主要内容容器不为空
        for selector in selectors_to_keep:
            try:
                elements = page.query_selector_all(selector)
                for element in elements:
                    # 如果容器为空，则移除
                    if not element.inner_html().strip():
                        element.remove()
            except Exception as e:
                continue
        
        # 获取清洗后的HTML
        cleaned_html = page.content()
        browser.close()
        
        return cleaned_html

def analyze_page_with_playwright(url):
    """
    使用Playwright下载网页HTML页面并清洗装饰元素
    """
    print(f"正在使用Playwright分析页面: {url}")
    
    # 创建Playwright工具类实例
    with PlaywrightUtils() as utils:
        # 打开页面
        page = utils.open_page(url)
        
        # 等待页面加载（可以根据需要添加更具体的等待条件）
        page.wait_for_timeout(3000)  # 等待3秒
        
        # 获取页面基本信息
        page_title = page.title()
        page_html = page.content()
    
    # 清洗HTML中的装饰元素
    cleaned_html = clean_decorative_elements(page_html)
    
    print(f"已获取页面: {page_title}")
    return {
        'url': url,
        'title': page_title,
        'html': cleaned_html  # 返回清洗后的HTML
    }

def analyze_container_with_deepseek_list(page_data):
    """使用DeepSeek API分析容器结构，输出变量名=值格式"""
    print("AI正在努力找目标中。。。。")
    prompt = f"""
你是一个专业的网页解析助手。请分析以下页面结构，找出适合用于网页采集的主要容器，并直接输出其他采集程序所需的变量，格式为“变量名称=值”，每行一个变量。
list_container_selector 为主要列表所在容器
article_item_selector 为文章在主要列表中的容器

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面html: {page_data['html']}

请直接输出如下变量（如能判断）：
list_container_selector=
article_item_selector=

每个变量一行，值为CSS选择器或XPATH，若无法判断可留空。
不要输出多余解释或格式说明，只输出变量名=值。
"""
    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3,
        max_tokens=500
    )
    return response.choices[0].message.content

def analyze_container_with_deepseek_words(page_data):
    print("AI正在努力找寻中。。。。")
    """使用DeepSeek API分析容器结构，输出变量名=值格式"""
    prompt = f"""
你是一个专业的网页解析助手。请分析以下页面结构，找出适合用于网页采集的正文容器，并直接输出其他采集程序所需的变量，格式为“变量名称=值”，每行一个变量。
date_selector 为文章日期所在容器
source_selector 为文章来源所在容器
content_selectors 为文章内容所在容器，多个容器用英文逗号隔开
title_selector 为文章标题所在容器

页面标题: {page_data['title']}
页面URL: {page_data['url']}
页面html: {page_data['html']}

请直接输出如下变量（如能判断）：
date_selector=
source_selector=
content_selectors=
tile_selector=

每个变量一行，值为CSS 选择器或XPATH ，若无法判断可留空。
不要输出多余解释或格式说明，只输出变量名=값。
"""
    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3,
        max_tokens=500
    )
    print("AI完成任务")
    return response.choices[0].message.content
def extract_first_article_url(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        driver = None
        try:
            driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
            _, _, article_links, _ = crawler_playwright.get_article_links(
                driver, list_url,
                list_container_selector or "body",  # 确保选择器不为空
                article_item_selector,
                list_container_type,
                article_item_type
            )
            
            for href in article_links[:3]:  # 最多尝试前3个链接
                full_url = crawler_playwright.get_full_link(
                    href, 
                    list_url, 
                    base_url or list_url, 
                    url_mode
                )
                if full_url:
                    return full_url
        except Exception:
            pass
        finally:
            if driver:
                driver.quit()
        return None

def test_deepseek_api():
    """简单的DeepSeek API测试函数"""
    print("正在测试DeepSeek API连接...")
    try:
        test_prompt = "请回复'API连接成功'以确认服务正常"
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": test_prompt}],
            temperature=0.1,
            max_tokens=20
        )
        result = response.choices[0].message.content
        print(f"API测试成功，响应: {result}")
        return True
    except Exception as e:
        print(f"API测试失败，错误: {str(e)}")
        return False

def main(url):
    """主函数：执行分析流程"""
    # 新增API测试步骤
    if not test_deepseek_api():
        print("API连接失败，程序终止")
        return
    
    # 步骤1: 使用Playwright获取页面结构
    page_data = analyze_page_with_playwright(url)  # 替换为新的Playwright实现
    
    # 步骤2: 使用DeepSeek分析容器
    analysis_result = analyze_container_with_deepseek_list(page_data)
    
    print("\n===== 列表容器分析结果 =====")
    print(analysis_result)
    
    # 解析AI返回的结果
    selectors = {}
    for line in analysis_result.splitlines():
        if '=' in line:
            key, value = line.split('=', 1)
            selectors[key.strip()] = value.strip()
    
    # 获取选择器值，如果没有则使用空字符串
    list_container_selector = selectors.get('list_container_selector', 'body')
    article_item_selector = selectors.get('article_item_selector', 'a[href*="article"]')
    
    # 调用提取函数
    if article_item_selector:
        article_url = extract_first_article_url(
            list_url=url,
            article_item_selector=article_item_selector,
            list_container_selector=list_container_selector
        )
        if not article_url:
            print("\n未能提取到有效的文章链接，流程终止。")
            return
        full_url = crawler_playwright.get_full_link(
            article_url,
            input_url=url,
            base_url=url,  # 使用列表页URL作为base_url
            url_mode="absolute"
        )
        
        print("\n===== 获取文章链接结果 =====")
        print(full_url)
    else:
        print("\n警告：未获取到有效的文章项选择器")
        return

    page_data = analyze_page_with_playwright(article_url)

    analysis_result2 = analyze_container_with_deepseek_words(page_data)

    print("\n===== 正文容器分析结果 =====")
    print(analysis_result2)


if __name__ == "__main__":
    # 示例URL - 替换为你需要分析的网页
    input_url = "https://www.hzrd.gov.cn/col/col1229690466/index.html"  
    main(input_url)