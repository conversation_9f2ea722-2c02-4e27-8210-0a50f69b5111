import json
import os
from collections import OrderedDict

class ConfigManager:
    def __init__(self, config_file="config.json", max_groups=100):
        self.config_file = config_file
        self.max_groups = max_groups
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件，如果不存在则创建默认配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f, object_pairs_hook=OrderedDict)
            except:
                # 如果配置文件损坏，创建新的配置
                return self.create_default_config()
        else:
            return self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置，包含所有参数字段"""
        return OrderedDict({
            "last_used": "default",
            "groups": OrderedDict({
                "default": {
                    "input_url": "",
                    "base_url": "",
                    "max_pages": "0",
                    "list_container_selector": "",
                    "list_container_type": "CSS",
                    "article_item_selector": "",
                    "article_item_type": "CSS",
                    "title_selector": "",
                    "title_selector_type": "CSS",
                    "content_selectors": "",
                    "content_type": "CSS",
                    "date_selector": "",
                    "date_selector_type": "CSS",
                    "source_selector": "",
                    "source_selector_type": "CSS",
                    "page_suffix": "index_{n}.html",
                    "page_suffix_start": 1,
                    "url_mode": "absolute",
                    "browser": "Firefox",
                    "headless": True,
                    "window_size": "",
                    "page_load_strategy": "normal",
                    "collect_links": True,
                    "mode": "balance",
                    "filters": [],
                    "export_filename": "",
                    "classid": ""
                }
            })
        })
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)
    
    def get_groups(self):
        """获取所有配置组名称"""
        return list(self.config["groups"].keys())
    
    def get_group(self, group_name):
        """获取指定配置组"""
        return self.config["groups"].get(group_name)
    
    def get_current_group(self):
        """获取当前使用的配置组"""
        return self.config["last_used"]
    
    def add_group(self, group_name, config_data):
        """添加新的配置组"""
        groups = self.config["groups"]
        
        # 如果配置组已存在，则更新
        if group_name in groups:
            groups[group_name] = config_data
        else:
            # 如果达到最大组数限制，删除最旧的一个
            if len(groups) >= self.max_groups:
                oldest = next(iter(groups))
                del groups[oldest]
            
            groups[group_name] = config_data
        
        self.config["last_used"] = group_name
        self.save_config()
    
    def delete_group(self, group_name):
        """删除配置组"""
        if group_name in self.config["groups"]:
            del self.config["groups"][group_name]
            
            # 如果删除的是当前使用的配置组，重置为默认
            if self.config["last_used"] == group_name:
                self.config["last_used"] = "default" if "default" in self.config["groups"] else next(iter(self.config["groups"].keys()), None)
            
            self.save_config()
            return True
        return False
    
    def set_current_group(self, group_name):
        """设置当前使用的配置组"""
        if group_name in self.config["groups"]:
            self.config["last_used"] = group_name
            self.save_config()
            return True
        return False