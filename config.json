{"last_used": "北京人大", "groups": {"default": {"input_url": "", "base_url": "", "max_pages": "0", "list_container_selector": "", "article_item_selector": "", "content_selectors": "", "date_selector": "", "source_selector": "", "page_suffix": "index_{n}.html", "url_mode": "input_url"}, "北京人大": {"input_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "base_url": "https://www.bjrd.gov.cn/rdzl/rdzc/rdzd/", "max_pages": "1", "list_container_selector": ".ty_gl.container", "list_container_type": "CSS", "article_item_selector": ".ty_list li", "article_item_type": "CSS", "title_selector": ".xl_title clearfix h1", "title_selector_type": "CSS", "content_selectors": ".<PERSON><PERSON><PERSON><PERSON>, .view", "content_type": "CSS", "date_selector": ".xl_ly span:first-child", "date_selector_type": "CSS", "source_selector": ".xl_ly span:last-child", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "relative", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": true, "mode": "balance", "filters": []}, "宁波人大": {"input_url": "https://www.nbrd.gov.cn/col/col1229576425/index.html", "base_url": "https://www.nbrd.gov.cn/col/col1229576425/", "max_pages": "2", "list_container_selector": ".default_pgContainer ul", "list_container_type": "CSS", "article_item_selector": ".default_pgContainer ul li", "article_item_type": "CSS", "title_selector": ".z_title color02", "title_selector_type": "CSS", "content_selectors": ".article_cont", "content_type": "CSS", "date_selector": "//li[contains(@class, \"color02\") and contains(text(), \"时间：", "date_selector_type": "XPath", "source_selector": "//li[contains(@class, \"color02\") and contains(text(), \"来源：", "source_selector_type": "XPath", "page_suffix": "index.html?uid=8022500&pageNum={n}", "page_suffix_start": 2, "url_mode": "relative", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": true, "mode": "balance", "filters": []}, "杭州人大": {"input_url": "https://www.hzrd.gov.cn/col/col1229690487/index.html", "base_url": "https://www.hzrd.gov.cn/col/col1229690487/", "max_pages": "0", "list_container_selector": ".lists_sub_1 ul", "list_container_type": "CSS", "article_item_selector": ".lists_sub_1 ul li", "article_item_type": "CSS", "title_selector": ".article_title", "title_selector_type": "CSS", "content_selectors": ".article_article", "content_type": "CSS", "date_selector": ".article_time span:first-child", "date_selector_type": "CSS", "source_selector": ".article_time span:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "index.html?uid=7856661&pageNum={n}", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "normal", "collect_links": false, "mode": "balance", "filters": []}, "武汉人大": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_40_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_议案建议.csv", "classid": "3802"}, "武汉人大_议案建议": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbjy/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_40_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_议案建议.csv", "classid": "3802"}, "武汉人大_基层建设": {"input_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbhd/index.html", "base_url": "http://www.whrd.gov.cn/html/rdlz/dbgz/dbhd/", "max_pages": "0", "list_container_selector": ".rq-news ul.RQNS-noLine", "list_container_type": "CSS", "article_item_selector": ".rq-news ul.RQNS-noLine li", "article_item_type": "CSS", "title_selector": ".RQVW-title", "title_selector_type": "CSS", "content_selectors": ".RQVW-desc;.canvasWrapper", "content_type": "CSS", "date_selector": ".RQVW-one div:first-child", "date_selector_type": "CSS", "source_selector": ".RQVW-one div:nth-child(2)", "source_selector_type": "CSS", "page_suffix": "list_42_{n}.html", "page_suffix_start": 2, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "武汉人大_基层人大.csv", "classid": "3806"}, "贵阳人大_立法聚焦": {"input_url": "https://www.gysrd.gov.cn/lfjw/lfjj/", "base_url": "https://www.gysrd.gov.cn/lfjw/lfjj/", "max_pages": "1", "list_container_selector": ".list_box", "list_container_type": "CSS", "article_item_selector": ".list_box li", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain .trs_editor_view", "content_type": "CSS", "date_selector": "[name=\"PubDate\"]", "date_selector_type": "CSS", "source_selector": "[name=\"ContentSource\"]", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "safe", "filters": [], "export_filename": "", "classid": ""}, "贵阳人大_监督工作": {"input_url": "http://www.gysrd.gov.cn/jdzx/jddt/", "base_url": "http://www.gysrd.gov.cn/jdzx/jddt/", "max_pages": "1", "list_container_selector": ".list_box", "list_container_type": "CSS", "article_item_selector": ".list_box li", "article_item_type": "CSS", "title_selector": ".xw_title", "title_selector_type": "CSS", "content_selectors": ".detailsMain .trs_editor_view", "content_type": "CSS", "date_selector": "[name=\"PubDate\"]", "date_selector_type": "CSS", "source_selector": "[name=\"ContentSource\"]", "source_selector_type": "CSS", "page_suffix": "index_{n}.html", "page_suffix_start": 1, "url_mode": "absolute", "browser": "Firefox", "headless": true, "window_size": "", "page_load_strategy": "eager", "collect_links": false, "mode": "balance", "filters": [], "export_filename": "", "classid": ""}}}