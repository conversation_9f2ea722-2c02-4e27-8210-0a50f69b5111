import re
from bs4 import BeautifulSoup, NavigableString, Tag

def normalize_date(date_str):
    """
    标准化日期字符串，支持多种常见格式，返回yyyy/mm/dd格式。
    """
    date_str = date_str.strip()
    if not date_str:
        return ""
    # 优先匹配 YYYY年MM月DD日（可带时间）
    m = re.search(r'(\d{4})[年\-/\.](\d{1,2})[月/\-.](\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配2位年份，自动补20xx
    m = re.search(r'(\d{2})[年\-/\.](\d{1,2})[月/\-.](\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配YYYYMMDD或YYYY-MM-DD等
    m = re.search(r'(\d{4})[\-/\.](\d{1,2})[\-/\.](\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    m = re.search(r'(\d{2})[\-/\.](\d{1,2})[\-/\.](\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 8位数字如20250625
    m = re.search(r'(\d{4})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth}/{d}"
    m = re.search(r'(\d{2})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth}/{d}"
    # 若未匹配到任何格式，直接返回原始字符串
    return date_str

def normalize_source(src_str):
    """
    标准化来源字符串，去除前缀、特殊符号等。
    支持的前缀包括：来源：、来源:、信息来源：、信息来源:、来源单位：、来源单位:
    """
    src_str = src_str.strip()
    # 定义需要去除的来源前缀列表
    source_prefixes = [
        "来源：", "来源:", 
        "信息来源：", "信息来源:",
        "来源单位：", "来源单位:",
        "文章来源：", "文章来源:"
    ]
    
    # 逐个检查并去除前缀
    for prefix in source_prefixes:
        if src_str.startswith(prefix):
            src_str = src_str[len(prefix):].lstrip()
            break
    
    return src_str

def clean_html_content(html):
    """
    清洗HTML正文，去除无关标签，保留结构化文本。
    """
    soup = BeautifulSoup(html, 'html.parser')
    # 移除不需要的标签
    for element in soup(['script', 'style', 'meta', 'link', 'o:p']):
        element.decompose()
    # 处理Office文档命名空间声明
    for attr in ['xmlns:o', 'xmlns:v', 'xmlns:w']:
        if attr in soup.html.attrs:
            del soup.html[attr]
    
    # 处理特殊换行标签
    for br in soup.find_all('br'):
        br.replace_with('\n')
    def process_node(node):
        if isinstance(node, NavigableString):
            text = node.strip()
            return text + ' ' if text else ''
        if isinstance(node, Tag):
            content = ''.join(process_node(child) for child in node.contents)
            if node.name == 'p':
                return content.rstrip() + ('\n' if not content.endswith('\n') else '')
            return content
    text = process_node(soup)
    # 后处理：清理多余空白
    text = re.sub(r'[ \t\xa0]+', ' ', text)
    text = re.sub(r' *\n *', '\n', text)
    text = re.sub(r'\n{3,}', '\n\n', text)
    text = re.sub(r' +\n', '\n', text)
    return text.strip()

# 全局黑名单正则（可扩展）
FILTER_BLACKLIST = [
    r'ZJEG_RSS\.content\.begin',
    r'ZJEG_RSS\.content\.end',
]

def filter_content(text, patterns=None):
    """增强型内容过滤"""
    if text is None:  # 处理空内容
        return ""
    
    # 合并全局黑名单和自定义过滤规则
    all_patterns = FILTER_BLACKLIST.copy()
    if patterns:
        if not isinstance(patterns, list):
            patterns = [patterns]
        all_patterns.extend(patterns)
    
    # 执行多条件过滤（新增异常捕获）
    for pattern in all_patterns:
        try:
            text = re.sub(pattern, '', text)
        except (TypeError, re.error) as e:
            print(f"过滤规则异常: {pattern} ({str(e)})")
            continue
            
    return text.strip()

def clean_title(title):
    """
    标准化标题，去除首尾空白、特殊符号、换行等。
    """
    if not isinstance(title, str):
        return ''
    title = title.strip()
    # 可根据需要扩展更多清洗规则
    title = title.replace('\n', ' ').replace('\r', ' ')
    title = re.sub(r'[\s\u3000]+', ' ', title)  # 合并多余空白
    title = re.sub(r'[\u200b\ufeff]', '', title)  # 去除零宽字符
    return title

